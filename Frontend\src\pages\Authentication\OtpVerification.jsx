import React, { useState, useRef, useEffect } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/OtpVerification.css";
import { verifyOTP, sendOTP, reset } from "../../redux/slices/authSlice";
import toast from "../../utils/toast";
import { getSellerRedirectPath } from "../../utils/sellerUtils";

const OtpVerification = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { isLoading, isError, isSuccess, error } = useSelector((state) => state.auth);

  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const inputRefs = useRef([]);
  const [localError, setLocalError] = useState("");
  const [cooldownTime, setCooldownTime] = useState(0);
  const [canResend, setCanResend] = useState(true);
  const [developmentOtp, setDevelopmentOtp] = useState(null);

  // Get data from location state
  const userId = location.state?.userId;
  const email = location.state?.email || "your email";
  const phoneNumber = location.state?.phoneNumber; // Keep for backward compatibility
  const cooldownSeconds = location.state?.cooldownSeconds || 60;
  const isLogin = location.state?.isLogin || false;
  const isCompletingRegistration = location.state?.isCompletingRegistration || false;
  const initialDevelopmentOtp = location.state?.developmentOtp;

  // Focus on first input when component mounts
  useEffect(() => {
    if (inputRefs.current[0]) {
      inputRefs.current[0].focus();
    }

    // Redirect if no userId
    if (!userId) {
      toast.error("Invalid access. Please try again.");
      navigate(isLogin ? "/auth" : "/signup");
    }

    // Start cooldown timer
    if (cooldownSeconds > 0) {
      setCooldownTime(cooldownSeconds);
      setCanResend(false);
    }
  }, []);

  // Cooldown timer effect
  useEffect(() => {
    let timer;
    if (cooldownTime > 0) {
      timer = setTimeout(() => {
        setCooldownTime(cooldownTime - 1);
      }, 1000);
    } else if (cooldownTime === 0 && !canResend) {
      setCanResend(true);
    }

    return () => clearTimeout(timer);
  }, [cooldownTime, canResend]);

  // Set development OTP if available from backend response
  useEffect(() => {
    if (initialDevelopmentOtp) {
      setDevelopmentOtp(initialDevelopmentOtp);
    }
  }, [userId, initialDevelopmentOtp]);

  const handleChange = (e, index) => {
    const value = e.target.value;

    // Only allow numbers
    if (!/^\d*$/.test(value)) return;

    // Update the OTP array
    const newOtp = [...otp];
    newOtp[index] = value.slice(-1); // Only take the last character
    setOtp(newOtp);

    // Clear any previous errors
    if (localError) setLocalError("");

    // Auto-focus next input if value is entered
    if (value && index < 5) {
      inputRefs.current[index + 1].focus();
    }

    // Auto-verify when last digit is entered
    if (value && index === 5) {
      // Check if all OTP fields are now filled
      const updatedOtp = [...newOtp];
      updatedOtp[index] = value.slice(-1);

      if (updatedOtp.every(digit => digit !== "")) {
        // All digits filled, auto-verify after a short delay
        setTimeout(() => {
          handleVerify(updatedOtp);
        }, 100);
      }
    }
  };

  const handleKeyDown = (e, index) => {
    // Handle backspace
    if (e.key === "Backspace") {
      if (!otp[index] && index > 0) {
        // If current input is empty and backspace is pressed, focus previous input
        const newOtp = [...otp];
        newOtp[index - 1] = "";
        setOtp(newOtp);
        inputRefs.current[index - 1].focus();
      }
    }

    // Handle left arrow key
    if (e.key === "ArrowLeft" && index > 0) {
      inputRefs.current[index - 1].focus();
    }

    // Handle right arrow key
    if (e.key === "ArrowRight" && index < 5) {
      inputRefs.current[index + 1].focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").trim();

    // Check if pasted content is a 6-digit number
    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split("");
      setOtp(newOtp);

      // Focus the last input
      inputRefs.current[5].focus();

      // Auto-verify after paste since all digits are filled
      setTimeout(() => {
        handleVerify(newOtp);
      }, 100);
    }
  };

  const handleResend = async () => {
    if (!canResend || isLoading) return;

    // Reset OTP fields
    setOtp(["", "", "", "", "", ""]);
    setLocalError("");
    dispatch(reset());

    // Focus first input
    inputRefs.current[0].focus();

    try {
      // For signup resend, use userId. For login resend, use email
      let requestData;
      if (userId && !isLogin) {
        // This is a signup resend - use userId
        requestData = { userId };
      } else {
        // This is a login resend - use email
        requestData = { email };
      }

      // Send OTP request
      const result = await dispatch(sendOTP(requestData)).unwrap();

      // Show success message
      toast.otp.success("OTP resent successfully!");

      // Start cooldown timer
      const newCooldown = result.cooldownSeconds || 60;
      setCooldownTime(newCooldown);
      setCanResend(false);

      // Update development OTP if provided
      if (result.developmentOtp) {
        setDevelopmentOtp(result.developmentOtp);
      }

    } catch (error) {
      console.error("Resend OTP error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Failed to resend OTP. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle different types of errors
      if (errorMessage.includes('wait') && errorMessage.includes('seconds')) {
        // Cooldown error
        const seconds = errorMessage.match(/\d+/)?.[0] || 60;
        toast.otp.cooldown(parseInt(seconds));
        setCooldownTime(parseInt(seconds));
        setCanResend(false);
      } else {
        // Show the actual backend error message
        toast.error(errorMessage);
      }
    }
  };

  const handleVerify = async (providedOtp = null) => {
    const currentOtp = providedOtp || otp;

    // Check if all OTP fields are filled
    if (currentOtp.some((digit) => digit === "")) {
      setLocalError("Please enter the complete OTP");
      return;
    }

    if (isLoading) return;

    const otpString = currentOtp.join("");

    // Reset any previous errors
    setLocalError("");
    dispatch(reset());

    try {
      // Verify OTP
      const result = await dispatch(verifyOTP({
        userId,
        otp: otpString
      })).unwrap();

      // Show success message
      toast.otp.verificationSuccess();

      // Navigate based on user role and onboarding status
      const userRole = result.user?.role || 'buyer';
      if (userRole === 'seller') {
        // For sellers, check onboarding status and redirect accordingly
        const redirectPath = getSellerRedirectPath(result.user);
        navigate(redirectPath);
      } else if (userRole === 'admin') {
        navigate("/admin/dashboard");
      } else {
        navigate("/content");
      }

    } catch (error) {
      console.error("OTP verification error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Verification failed. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle verification errors
      if (errorMessage.includes('Invalid OTP')) {
        setLocalError("Invalid OTP. Please try again.");
        toast.otp.verificationError();
      } else if (errorMessage.includes('expired')) {
        setLocalError("OTP has expired. Please request a new one.");
        toast.error("OTP has expired. Please request a new one.");
      } else {
        setLocalError(errorMessage);
        toast.error(errorMessage);
      }
    }
  };

  return (
    <div className="otp-page otp-container">
      <div className="otp-form-container">
        <h1 className="otp-title">
          {isCompletingRegistration ? "Complete Registration" : "OTP Verification"}
        </h1>

        <p className="otp-instruction">
          {isCompletingRegistration
            ? `Complete your registration by verifying the OTP sent to ${email}`
            : (developmentOtp && !import.meta.env.DEV
              ? `Enter The OTP Displayed Below`
              : `Enter The OTP Sent To ${email}`)}
        </p>

        {/* OTP Display - Show when backend provides developmentOtp (development mode or OTP disabled) */}
        {developmentOtp && (
          <div className="development-otp-display" style={{
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '8px',
            padding: '12px',
            margin: '16px 0',
            textAlign: 'center'
          }}>
            <p style={{ margin: '0 0 8px 0', fontSize: '14px', color: '#856404' }}>
              <strong>{import.meta.env.DEV ? 'Development Mode - OTP:' : 'Your OTP:'}</strong>
            </p>
            <p style={{
              margin: '0',
              fontSize: '24px',
              fontWeight: 'bold',
              color: '#856404',
              letterSpacing: '4px'
            }}>
              {developmentOtp}
            </p>
          </div>
        )}

        <div className="otp-input-group">
          {otp.map((digit, index) => (
            <input
              key={index}
              type="text"
              maxLength={1}
              value={digit}
              onChange={(e) => handleChange(e, index)}
              onKeyDown={(e) => handleKeyDown(e, index)}
              onPaste={index === 0 ? handlePaste : null}
              ref={(el) => (inputRefs.current[index] = el)}
              className="otp-input"
              aria-label={`Digit ${index + 1} of OTP`}
            />
          ))}
        </div>

        {localError && <p className="otp-error">{localError}</p>}

        <div className="otp-resend">
          <span>Didn't Received OTP? </span>
          <button
            onClick={handleResend}
            className="resend-button"
            disabled={!canResend || isLoading}
          >
            {!canResend && cooldownTime > 0
              ? `Resend in ${cooldownTime}s`
              : isLoading
                ? "Sending..."
                : "Resend"
            }
          </button>
        </div>

        <button
          onClick={handleVerify}
          className="verify-button btn-primary"
          disabled={isLoading}
        >
          {isLoading ? "Verifying..." : "Verify"}
        </button>

        <div className="back-to-signin">
          <Link to="/auth">Back To Sign In</Link>
        </div>
      </div>
    </div>
  );
};

export default OtpVerification;
